/**
 * MCP系统重构测试脚本
 * 运行: node test-mcp-refactor.js
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('=== MCP系统重构测试 ===\n');

// 编译TypeScript并运行测试
const testProcess = spawn('npx', ['ts-node', 'src/test/mcp-refactor-test.ts'], {
  cwd: __dirname,
  stdio: 'inherit'
});

testProcess.on('error', (error) => {
  console.error('测试进程启动失败:', error);
  process.exit(1);
});

testProcess.on('exit', (code) => {
  console.log(`\n测试进程退出，代码: ${code}`);
  
  if (code === 0) {
    console.log('\n✓ 所有测试完成');
  } else {
    console.log('\n✗ 测试过程中出现错误');
  }
  
  process.exit(code);
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n收到中断信号，正在停止测试...');
  testProcess.kill('SIGINT');
});
