/**
 * MCP系统重构测试
 * 测试新的MCP客户端实现
 */

import { MCPClient } from '../shared/mcp-client';
import { MCPManager } from '../shared/mcp-manager';

async function testRefactoredMCPClient() {
  console.log('=== 测试重构后的MCP客户端 ===');
  
  const client = new MCPClient();
  
  // 测试stdio连接（文件系统服务器）
  console.log('\n1. 测试stdio连接...');
  try {
    const connection = await client.connect({
      id: 'filesystem-stdio',
      name: 'Filesystem Server (stdio)',
      url: 'stdio:npx:@modelcontextprotocol/server-filesystem:.',
      enabled: true,
      autoConnect: true,
      auth: { type: 'none' }
    });
    
    console.log('✓ Stdio连接成功:', {
      id: connection.id,
      name: connection.name,
      status: connection.status,
      toolsCount: connection.tools.length,
      resourcesCount: connection.resources.length
    });
    
    // 测试工具调用
    if (connection.tools.length > 0) {
      console.log('\n2. 测试工具调用...');
      const firstTool = connection.tools[0];
      console.log(`尝试调用工具: ${firstTool.name}`);
      
      try {
        const result = await client.callTool(connection.id, firstTool.name, {});
        console.log('✓ 工具调用成功:', result);
      } catch (error) {
        console.log('⚠ 工具调用失败 (可能需要参数):', error instanceof Error ? error.message : error);
      }
    }
    
    // 获取所有可用工具
    console.log('\n3. 获取所有可用工具...');
    const allTools = client.getAllAvailableTools();
    console.log('✓ 可用工具列表:', allTools.map(t => ({
      name: t.name,
      description: t.description,
      serverId: t.serverId,
      serverName: t.serverName
    })));
    
    // 断开连接
    console.log('\n4. 断开连接...');
    client.disconnect(connection.id);
    console.log('✓ 连接已断开');
    
  } catch (error) {
    console.error('✗ Stdio连接测试失败:', error);
  }
  
  // 测试HTTP连接
  console.log('\n5. 测试HTTP连接...');
  try {
    const httpConnection = await client.connect({
      id: 'http-server',
      name: 'HTTP MCP Server',
      url: 'http://localhost:3001/mcp',
      enabled: true,
      autoConnect: true,
      auth: { type: 'none' }
    });
    
    console.log('✓ HTTP连接成功:', {
      id: httpConnection.id,
      name: httpConnection.name,
      status: httpConnection.status
    });
    
    client.disconnect(httpConnection.id);
    console.log('✓ HTTP连接已断开');
    
  } catch (error) {
    console.log('⚠ HTTP连接测试失败 (服务器可能未运行):', error instanceof Error ? error.message : error);
  }
}

async function testMCPManagerWithTools() {
  console.log('\n=== 测试MCP管理器工具集成 ===');
  
  const manager = new MCPManager();
  
  try {
    // 启动文件系统服务器
    console.log('\n1. 启动文件系统服务器...');
    await manager.startFilesystemServer();
    console.log('✓ 文件系统服务器已启动');
    
    // 获取所有工具
    console.log('\n2. 获取所有工具...');
    const tools = await manager.getAllTools();
    console.log('✓ 可用工具:', tools.map(t => ({
      name: t.name,
      description: t.description,
      serverId: t.serverId
    })));
    
    // 测试AI对话与工具调用
    console.log('\n3. 测试AI对话与工具调用...');
    const testMessages = [
      { role: 'user' as const, content: '请帮我搜索当前目录下的TypeScript文件' }
    ];
    
    try {
      const result = await manager.handleAIConversationWithTools(testMessages);
      console.log('✓ AI对话成功:', {
        response: result.response.substring(0, 200) + '...',
        toolResultsCount: result.toolResults.length
      });
    } catch (error) {
      console.log('⚠ AI对话失败 (可能需要OpenAI配置):', error instanceof Error ? error.message : error);
    }
    
  } catch (error) {
    console.error('✗ MCP管理器测试失败:', error);
  }
}

async function runAllTests() {
  console.log('开始MCP系统重构测试...\n');
  
  await testRefactoredMCPClient();
  await testMCPManagerWithTools();
  
  console.log('\n=== 测试完成 ===');
  console.log('\n重构要点总结:');
  console.log('1. ✓ 移除了SSEClientTransport，改用StreamableHTTPClientTransport');
  console.log('2. ✓ stdio类型的MCP server直接在mcp-client中初始化');
  console.log('3. ✓ 添加了进程管理，确保正确关闭stdio进程');
  console.log('4. ✓ 在LLM调用时附加支持的工具列表');
  console.log('5. ✓ mockApi中也支持本地进程启动MCP server');
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error);
}

export { testRefactoredMCPClient, testMCPManagerWithTools, runAllTests };
